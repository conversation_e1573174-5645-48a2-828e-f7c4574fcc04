package session

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/log"
	"github.com/sayeworldevelopment/wp-core/pkg/nat"
	"github.com/sayeworldevelopment/wp-core/pkg/proxy"
	"github.com/sayeworldevelopment/wp-core/pkg/wrapper"
	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/types"
	"go.mau.fi/whatsmeow/types/events"
	waLog "go.mau.fi/whatsmeow/util/log"
)

type Service interface {
	CreateSession(ctx context.Context, req dtos.CreateSessionReq) (dtos.SessionResponse, error)
	GetSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	UpdateSession(ctx context.Context, req dtos.UpdateSessionReq) (dtos.SessionResponse, error)
	DeleteSession(ctx context.Context, id uuid.UUID) error
	ListSessions(ctx context.Context, status string, page, perPage int) ([]dtos.SessionResponse, int64, error)
	ConnectSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	DisconnectSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	PauseSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	ResumeSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	GetSessionEvents(ctx context.Context, sessionID uuid.UUID, page, perPage int) ([]dtos.SessionEventResponse, error)
	SubscribePresence(ctx context.Context, req dtos.SessionSubscriptionReq) error
	GetPresences(ctx context.Context, sessionID uuid.UUID, page, perPage int) (dtos.PaginatedData, error)
	GetSubscriptions(ctx context.Context, sessionID uuid.UUID) ([]entities.SessionSubscription, error)
	UpdateSessionWithRegID(ctx context.Context, sessionID uuid.UUID, regID string) (dtos.SessionResponse, error)
	CleanWhatsmeowTables(ctx context.Context, ourJID string) error
}

// Helper functions to map entities to DTOs
func mapToSessionResponse(session entities.Session) dtos.SessionResponse {
	return dtos.SessionResponse{
		ID:                 session.ID,
		RegID:              session.RegID,
		JID:                session.JID,
		Status:             string(session.Status),
		LastConnected:      session.LastConnected,
		LastDisconnected:   session.LastDisconnected,
		ConnectionAttempts: session.ConnectionAttempts,
		ErrorMessage:       session.ErrorMessage,
		ProxyUsed:          session.ProxyUsed,
		AutoReconnect:      session.AutoReconnect,
		MessageCount:       session.MessageCount,
		CreatedAt:          session.CreatedAt,
	}
}

func mapToSessionEventResponse(event entities.SessionEvent) dtos.SessionEventResponse {
	return dtos.SessionEventResponse{
		ID:          event.ID,
		SessionID:   event.SessionID,
		EventType:   event.EventType,
		Description: event.Description,
		Timestamp:   event.Timestamp,
	}
}

type service struct {
	repo Repository
	wp   *wrapper.Client
}

// NewSessionService creates a new session service
func NewService(sessionRepo Repository, wp *wrapper.Client) Service {
	return &service{
		repo: sessionRepo,
		wp:   wp,
	}
}

func (s *service) CreateSession(ctx context.Context, req dtos.CreateSessionReq) (dtos.SessionResponse, error) {

	var session entities.Session
	if req.UseProxy {
		proxy_id, proxy_address, err := proxy.SetProxyInfoForSession(ctx, req.IP, req.E164PhoneNumber)
		if err != nil {
			return dtos.SessionResponse{}, errors.New("failed to set proxy: " + err.Error())
		}

		country, _ := proxy.DetectCountryByPhone(req.E164PhoneNumber)

		session = entities.Session{
			RegID:              "", // Will be set when login code is obtained
			JID:                "", // Will be set when login code is obtained
			Status:             entities.SessionStatusInitialized,
			ConnectionAttempts: 0,
			ProxyUsed:          req.UseProxy,
			ProxyAddress:       proxy_address,
			ProxyID:            proxy_id,
			DeviceInfo:         req.DeviceInfo,
			AutoReconnect:      req.AutoReconnect,
			Country:            country,
		}
	} else {
		session = entities.Session{
			RegID:              "", // Will be set when login code is obtained
			JID:                "", // Will be set when login code is obtained
			Status:             entities.SessionStatusInitialized,
			ConnectionAttempts: 0,
			ProxyUsed:          req.UseProxy,
			DeviceInfo:         req.DeviceInfo,
			AutoReconnect:      req.AutoReconnect,
		}
	}

	createdSession, err := s.repo.CreateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	event := entities.SessionEvent{
		SessionID:   createdSession.ID,
		EventType:   "created",
		Description: "Session created",
		Timestamp:   time.Now(),
	}
	s.repo.RecordSessionEvent(ctx, event)

	return mapToSessionResponse(createdSession), nil
}

func (s *service) GetSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	session, err := s.repo.GetSessionByID(ctx, id)
	if err != nil {
		return dtos.SessionResponse{}, err
	}
	return mapToSessionResponse(session), nil
}

func (s *service) UpdateSession(ctx context.Context, req dtos.UpdateSessionReq) (dtos.SessionResponse, error) {
	session, err := s.repo.GetSessionByID(ctx, req.ID)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Update fields if provided
	if req.Status != "" {
		session.Status = entities.SessionStatus(req.Status)
	}

	if req.AutoReconnect != nil {
		session.AutoReconnect = *req.AutoReconnect
	}

	if req.ProxyAddress != "" {
		session.ProxyAddress = req.ProxyAddress
		session.ProxyUsed = true
	}

	// Save the updated session
	err = s.repo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record session update event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "updated",
		Description: "Session updated",
		Timestamp:   time.Now(),
	}
	s.repo.RecordSessionEvent(ctx, event)

	return mapToSessionResponse(session), nil
}

func (s *service) DeleteSession(ctx context.Context, id uuid.UUID) error {
	// Get the session first to check if it exists
	session, err := s.repo.GetSessionByID(ctx, id)
	if err != nil {
		return err
	}

	// Disconnect the session if it's connected
	if session.Status == entities.SessionStatusConnected {
		_, err = s.DisconnectSession(ctx, id)
		if err != nil {
			return err
		}
	}

	// Delete the session
	return s.repo.DeleteSession(ctx, id)
}

func (s *service) ListSessions(ctx context.Context, status string, page, perPage int) ([]dtos.SessionResponse, int64, error) {
	offset := (page - 1) * perPage
	sessions, count, err := s.repo.ListSessions(ctx, status, perPage, offset)
	if err != nil {
		return nil, 0, err
	}

	var sessionResponses []dtos.SessionResponse
	for _, session := range sessions {
		sessionResponses = append(sessionResponses, mapToSessionResponse(session))
	}

	return sessionResponses, count, nil
}

func (s *service) ConnectSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	session, err := s.repo.GetSessionByID(ctx, id)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Update session status to connecting
	session.Status = entities.SessionStatusConnecting
	session.ConnectionAttempts++
	err = s.repo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record connection attempt event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "connecting",
		Description: fmt.Sprintf("Connection attempt #%d", session.ConnectionAttempts),
		Timestamp:   time.Now(),
	}
	s.repo.RecordSessionEvent(ctx, event)

	// Get the device from whatsmeow
	senderArr := strings.Split(session.JID, "@")
	sender := types.NewJID(senderArr[0], types.DefaultUserServer)
	device, err := s.wp.MContainer.GetDevice(sender)
	if err != nil || device == nil {
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Connect Session Failed",
				Message: "Connect Session Failed, get device err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      "unknown", // IP is not available in this context
			})
		}

		session.Status = entities.SessionStatusFailed
		session.ErrorMessage = "Device not found:"
		s.repo.UpdateSession(ctx, session)

		failEvent := entities.SessionEvent{
			SessionID:   session.ID,
			EventType:   "error",
			Description: "Device not found:",
			Timestamp:   time.Now(),
		}
		s.repo.RecordSessionEvent(ctx, failEvent)

		return mapToSessionResponse(session), errors.New("device not found")
	}

	// Create WhatsApp client
	clientLog := waLog.Stdout("Client", "DEBUG", true)
	client := whatsmeow.NewClient(device, clientLog)

	// Add event handler
	client.AddEventHandler(func(evt interface{}) {
		// Handle events and record them
		s.handleWhatsAppEvent(ctx, session.ID, evt)
	})

	// Connect to WhatsApp
	err = client.Connect()
	if err != nil {
		session.Status = entities.SessionStatusFailed
		session.ErrorMessage = "Connection failed: " + err.Error()
		s.repo.UpdateSession(ctx, session)

		failEvent := entities.SessionEvent{
			SessionID:   session.ID,
			EventType:   "error",
			Description: "Connection failed: " + err.Error(),
			Timestamp:   time.Now(),
		}
		s.repo.RecordSessionEvent(ctx, failEvent)

		return mapToSessionResponse(session), err
	}

	// Store the client in the global map
	nat.WaConnects[session.RegID] = client

	// Update session status to connected
	session.Status = entities.SessionStatusConnected
	session.LastConnected = time.Now()
	session.ErrorMessage = ""
	err = s.repo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record connection success event
	successEvent := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "connected",
		Description: "Successfully connected to WhatsApp",
		Timestamp:   time.Now(),
	}

	s.repo.CleanWhatsmeowTables(ctx, session.JID)

	s.repo.RecordSessionEvent(ctx, successEvent)

	return session.MapToSessionResponse(), nil
}

func (s *service) DisconnectSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	session, err := s.repo.GetSessionByID(ctx, id)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Get the client from the global map
	client := nat.WaConnects[session.RegID]
	if client != nil {
		// Disconnect the client
		client.Disconnect()
		// Remove from the global map
		delete(nat.WaConnects, session.RegID)
	}

	// Update session status
	session.Status = entities.SessionStatusDisconnected
	session.LastDisconnected = time.Now()
	err = s.repo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record disconnection event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "disconnected",
		Description: "Disconnected from WhatsApp",
		Timestamp:   time.Now(),
	}
	s.repo.RecordSessionEvent(ctx, event)

	return session.MapToSessionResponse(), nil
}

func (s *service) PauseSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	session, err := s.repo.GetSessionByID(ctx, id)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Can only pause connected sessions
	if session.Status != entities.SessionStatusConnected {
		return mapToSessionResponse(session), errors.New("can only pause connected sessions")
	}

	// Update session status
	session.Status = entities.SessionStatusPaused
	err = s.repo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record pause event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "paused",
		Description: "Session paused",
		Timestamp:   time.Now(),
	}
	s.repo.RecordSessionEvent(ctx, event)

	return mapToSessionResponse(session), nil
}

func (s *service) ResumeSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	session, err := s.repo.GetSessionByID(ctx, id)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Can only resume paused sessions
	if session.Status != entities.SessionStatusPaused {
		return mapToSessionResponse(session), errors.New("can only resume paused sessions")
	}

	// Update session status
	session.Status = entities.SessionStatusConnected
	err = s.repo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record resume event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "resumed",
		Description: "Session resumed",
		Timestamp:   time.Now(),
	}
	s.repo.RecordSessionEvent(ctx, event)

	return mapToSessionResponse(session), nil
}

func (s *service) GetSessionEvents(ctx context.Context, sessionID uuid.UUID, page, perPage int) ([]dtos.SessionEventResponse, error) {
	offset := (page - 1) * perPage
	events, err := s.repo.GetSessionEvents(ctx, sessionID, perPage, offset)
	if err != nil {
		return nil, err
	}

	var eventResponses []dtos.SessionEventResponse
	for _, event := range events {
		eventResponses = append(eventResponses, mapToSessionEventResponse(event))
	}

	return eventResponses, nil
}

func (s *service) SubscribePresence(ctx context.Context, req dtos.SessionSubscriptionReq) error {
	session, err := s.repo.GetSessionByID(ctx, req.SessionID)
	if err != nil {
		return err
	}

	// Can only subscribe with connected sessions
	if session.Status != entities.SessionStatusConnected {
		return errors.New("can only subscribe with connected sessions")
	}

	device, err := s.repo.FindActiveDeviceByRegID(ctx, session.RegID)
	if err != nil || device.RegistrationID == "" {
		return err
	}

	_, err = s.wp.SubscribePresence(ctx, device.JID, req.Phone)
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Subscribe Presence Failed",
			Message: "Subscribe Presence Failed, subscribe presence err:" + err.Error(),
			Entity:  "session",
			Type:    "error",
		})
		return err
	}

	// Create subscription record
	subscription := entities.SessionSubscription{
		SessionID: session.ID,
		Phone:     req.Phone,
		JID:       device.JID,
		Active:    true,
	}
	err = s.repo.CreateSubscription(ctx, subscription)
	if err != nil {
		return err
	}

	// Record subscription event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "subscribed",
		Description: "Subscribed to presence updates for " + req.Phone,
		Timestamp:   time.Now(),
	}
	s.repo.RecordSessionEvent(ctx, event)

	return nil
}

func (s *service) GetSubscriptions(ctx context.Context, sessionID uuid.UUID) ([]entities.SessionSubscription, error) {
	return s.repo.GetSubscriptions(ctx, sessionID)
}

func (s *service) GetPresences(ctx context.Context, sessionID uuid.UUID, page, perPage int) (dtos.PaginatedData, error) {
	return s.repo.GetPresences(ctx, sessionID, page, perPage)
}

// UpdateSessionWithRegID updates a session with registration ID and JID
func (s *service) UpdateSessionWithRegID(ctx context.Context, sessionID uuid.UUID, regID string) (dtos.SessionResponse, error) {
	// Get the session
	session, err := s.repo.GetSessionByID(ctx, sessionID)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	session.RegID = regID

	err = s.repo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record session update event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "reg_id_updated",
		Description: "Session updated with registration ID: " + regID,
		Timestamp:   time.Now(),
	}
	s.repo.RecordSessionEvent(ctx, event)

	return mapToSessionResponse(session), nil
}

// CleanWhatsmeowTables removes data from Whatsmeow tables for a specific JID
func (s *service) CleanWhatsmeowTables(ctx context.Context, ourJID string) error {
	return s.repo.CleanWhatsmeowTables(ctx, ourJID)
}

// handleWhatsAppEvent handles WhatsApp events and records them in the session events
func (s *service) handleWhatsAppEvent(ctx context.Context, sessionID uuid.UUID, evt interface{}) {
	// Record different types of events based on the event type
	// This is a simplified version, you can expand it to handle more event types
	switch evt.(type) {
	case *events.Disconnected:
		// Handle disconnection event
		session, err := s.repo.GetSessionByID(ctx, sessionID)
		if err != nil {
			return
		}

		session.Status = entities.SessionStatusDisconnected
		session.LastDisconnected = time.Now()
		s.repo.UpdateSession(ctx, session)

		event := entities.SessionEvent{
			SessionID:   sessionID,
			EventType:   "disconnected",
			Description: "Disconnected from WhatsApp",
			Timestamp:   time.Now(),
		}
		s.repo.RecordSessionEvent(ctx, event)

		// Remove from the global map
		delete(nat.WaConnects, session.RegID)
	}
}
